We have this printout in the jenkins log:
09:52:42 2025-07-04 08:52:42 elipy2 [WARNING]: Found orphaned build older than 2 days (age: 83.8 days): \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23280034

The orphan check should not only care for age on disk, it should check if that build not in bilbo, it should be deleted.
if it is in bilbo but marked as deleted in bilbo, it should be deleted.
Otherwise it should be kept regardless of age.

so logic should be:
if build not in bilbo and build is older than 2 days: delete build
if build in bilbo and marked as deleted: delete build
if build in bilbo and not marked as deleted: keep build

This build 23280034 is in bilbo but marked as drone and not marked as deleted, so it should be kept
Also we still have the inconsistent in the path with / and \, it should be all \
We should have a function of delete empty folder at the end of deletion
for example we deleted build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24198366\CH1-event\2419836
so if we have empty folder \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24198366\CH1-event
That folder also should be deleted and if there is nothing in 24198366, that should be deleted as well

All git monitored files which was updated should be run with black/lint and have green unittest