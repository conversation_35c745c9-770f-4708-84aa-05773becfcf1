import os
import sys

# Add the fbenv path
sys.path.append(r'C:\Users\<USER>\Perforce\DICE\hvu_DICE-HVU1_4408\TnT\Bin\fbenv')

# Set the GAME_ROOT environment variable
os.environ['GAME_ROOT'] = r'C:\Users\<USER>\Perforce\DICE\hvu_DICE-HVU1_4408'

print(f"GAME_ROOT environment variable: {os.environ.get('GAME_ROOT')}")
print(f"GAME_ROOT exists: {os.path.exists(os.environ.get('GAME_ROOT'))}")

# Try to import the module and test the bootstrap method
try:
    from fbenv.fbenvconfigservice_client import FbEnvConfigServiceSession
    session = FbEnvConfigServiceSession()
    print(f"Successfully created session with gameroot: {session._gameroot}")
except Exception as e:
    print(f"Error creating session: {e}")
    print(f"Error type: {type(e).__name__}")
    import traceback
    traceback.print_exc()
