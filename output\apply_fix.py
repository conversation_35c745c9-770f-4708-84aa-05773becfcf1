# Fix for fbenvconfigservice_service_tools.py to handle None gameroot
import os
import shutil

# Backup file path
backup_file = r"C:\Users\<USER>\vscode\output\fbenvconfigservice_service_tools.py.backup"
original_file = r"C:\Users\<USER>\Perforce\DICE\hvu_DICE-HVU1_4408\TnT\Bin\fbenv\fbenv\fbenvconfigservice_service_tools.py"

# Read the original file
with open(original_file, 'r', encoding='utf-8') as f:
    content = f.read()

# Replace the problematic line with a safer version
old_line = "        self.fbenvconfigservice_local_deploy_dir = os.path.join(self.gameroot,  self.fecs_exe_path)"
new_line = """        # Handle case where gameroot is None
        if self.gameroot is None:
            # Try to get from environment variable as fallback
            fallback_gameroot = os.environ.get('GAME_ROOT', 'C:\\Users\\<USER>\\Perforce\\DICE\\hvu_DICE-HVU1_4408')
            self.gameroot = fallback_gameroot
            print(f"Warning: gameroot was None, using fallback: {fallback_gameroot}")
        self.fbenvconfigservice_local_deploy_dir = os.path.join(self.gameroot,  self.fecs_exe_path)"""

if old_line in content:
    new_content = content.replace(old_line, new_line)
    
    # Write the modified content back
    with open(original_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("Successfully applied fix to fbenvconfigservice_service_tools.py")
    print(f"Backup saved at: {backup_file}")
    print("Modified line 43 to handle None gameroot case")
else:
    print("Could not find the exact line to replace. Content may have changed.")
    print("Looking for:")
    print(old_line)
