from .exception import *
import os
import psutil
import subprocess
import time
import signal
import ctypes
import shutil
import filecmp
from win32api import GetFileVersionInfo, LOWORD, HIWORD
from .lockfile import LockFile
from . import fbfileio
from distutils.dir_util import copy_tree
from distutils.errors import DistutilsFileError


class ServiceFilesQueryException(Exception):
    """Indicates that an issue was found querying the service to get file information"""
    def __init__(self, status, message):
        super(Exception, self).__init__(status, message)


class FECServiceNotRunningException(Exception):
    def __init__(self, msg):
        """
        Args:
            msg (str): the exception message
        """
        super(FECServiceNotRunningException, self).__init__(msg)

# Class used for interacting with the service process - launching, ensuring the right version is installed etc.
class FbenvConfigServiceTools():
    file_lock_name = "fbenv_lock"
    fbenvconfigservice_target_working_dir = os.path.normpath(os.path.join(os.environ.get("localappdata"), "FbEnvConfigService/win-x64"))
    should_print_output = True
    fecs_exe_path = "TnT/Build/FbEnvConfigService/bin/win-x64/"
    fecs_exe_name = "fbenvconfigservice.exe"
    file_interest_list = [fecs_exe_name, "drone.exe", "python.exe", "fbenvconsole.exe", "frostbitestartup.exe", "frostbitesetup.exe"]
    
    def __init__(self, gameroot:str, _session_api_instance):
        self.gameroot = gameroot
        self._session_api_instance = _session_api_instance
        self.fbenvconfigservice_local_deploy_dir = os.path.join(self.gameroot,  self.fecs_exe_path)
        self.version = "1"
        self.local_version_major_num, self.local_version_minor_num = self._get_local_bin_version_number()

    def initialize_service(self, _print_output = True, version = "1", retry_attempts = 5):
        self.version = version
        self.should_print_output = _print_output
        lock_file = os.path.normpath(os.path.join(os.environ.get("localappdata"), self.file_lock_name))

        #internal method to allow for retries on lock file acquisition        
        def initialize_internal(retry_attempts = 5):
            if retry_attempts < 0:
                return False
            try:
                with LockFile(lock_file, 'wb', timeout=6):
                    if self.service_is_running() is False:
                        # can't launch when not admin, issues arise. -> consider adding pyuac to launch as admin in future but admin / non-admin workflows need to be looked at in general at some time. 
                        if self._is_admin() is False:
                            retry_attempts = 0
                            raise Exception("FbEnvConfigService is not currently running and cannot be started if running as non-admin. Please relaunch as admin") 
                        
                        self._print_output("FbEnvConfigService is not currently running, checking for deployed version")
                        (remote_version_major, remote_version_minor) = self._get_remote_bin_version_number()
                        if remote_version_major is None or self.source_assemblies_newer_than_target(self.local_version_major_num, self.local_version_minor_num, remote_version_major, remote_version_minor) is True:
                            self._print_output("Detected that the locally deployed version, which is not currently running, of the fbenvconfigservice is older than the one in source. Copying over files")
                            if (remote_version_major is not None):
                                self._print_output("Local version: " + str(self.local_version_major_num) + "." + str(self.local_version_minor_num) + " Remote version: " + str(remote_version_major) + "." + str(remote_version_minor))
                            else:
                                self._print_output("No remote version, local version: " + str(self.local_version_major_num) + "." + str(self.local_version_minor_num))
                            return self.copy_launch_and_ensure_running()
                            
                        # can just launch the deployed version
                        return self._launch_service()
                    elif self._running_version_is_valid():
                        # service was already running, we can return.
                        return
                    else:
                        # service is running, but is older
                        self._print_output("Detected that current locally running version of the configservice is older than the one in source.")              
                        if self._kill_service() is False:
                            return initialize_internal(retry_attempts - 1)
                        return self.copy_launch_and_ensure_running()                
            
            except TimeoutError:
                if retry_attempts > 0:
                    self._print_output("Multiple instances attempting to launch service simultaneously, retrying in 1 second")
                    time.sleep(1)
                    return initialize_internal(retry_attempts - 1)
                else:
                    return False
            except FECServiceNotRunningException:
                return False
            except ServiceFilesQueryException:
                self._print_output("Issue checking for service files. Will proceed if service is running and available")
                if self.service_is_running(1) and self._get_running_version(1) is not None:
                    return True
                else:
                    return initialize_internal(retry_attempts - 1)             

        if initialize_internal(retry_attempts) is False:
            self.determine_installation_failure()
            return False
        return True
  
    # copies over binaries, launches the service and ensures it's running
    def copy_launch_and_ensure_running(self):
        self.copy_folder(self.fbenvconfigservice_local_deploy_dir, self.fbenvconfigservice_target_working_dir)
        return self._launch_service()

    # used to help establish why the service initialization failed
    def determine_installation_failure(self):
        self._print_output("FbEnvConfigService launch has failed!")
        if self.service_is_running(1) is False:
            are_equal = filecmp.cmp(self.fbenvconfigservice_local_deploy_dir, self.fbenvconfigservice_target_working_dir)
            if are_equal is False:
                self._print_output("Target folder contents:" + self.fbenvconfigservice_target_working_dir + " do not match local folder:" +self.fbenvconfigservice_local_deploy_dir+ "after copying over")
                # compare local to remote, if local is equal or newer then copy and launch and try again?
                (remote_version_major, remote_version_minor) = self._get_remote_bin_version_number()
                if self.source_assemblies_newer_than_target(remote_version_major, remote_version_minor, self.local_version_major_num, self.local_version_minor_num) is False:
                    self._print_output("Binary in target directory is not newer than local so attempting to copy over source binaries in case the copy failed")
                    return self.copy_launch_and_ensure_running()                

            self._print_output("Attempt to start the service has failed. Check that the destination folder: " + self.fbenvconfigservice_target_working_dir  + """has been copied over correctly.""" +
                            """\nEnsure the """ + self.fbenvconfigservice_local_deploy_dir + """folder has been synced correctly and try FrostbiteSetup or TnT/Bin/fbcli/cli.bat as admin again. """ +
                                """\nIf in need of further assistance, please reach out at the #frosbite-ew slack channel
                            """)
            return       
        self._print_output("""Issue connecting to the running FbEnvConfigService,  Check service status at http://localhost:7225/api/v1.0/service-info and logs at C:\\ProgramData\\Frostbite\\FbEnvConfigService\\logs
                          If the issue persists, try killing the fbenvconfigservice.exe process and running FrostbiteSetup or TnT/Bin/fbcli/cli.bat as admin)
                          If that doesn't help Please contact us at #frostbite-ew with relevant logs for assistance.""" )

    # copies over the folder and makes files writeable
    def copy_folder(self, source_directory, target_directory):
         source_directory = os.path.normpath(source_directory)
         target_directory = os.path.normpath(target_directory)
         try:
            copy_tree(source_directory, target_directory, update=True, verbose=True)
         except (DistutilsFileError, PermissionError) as e:
            if self.service_is_running(1) is True:
                self._print_output("Issue copying over files. Service is running, attempting to copy over files again")
                self._kill_service()
            self._set_all_files_to_writable(target_directory, True)
            try:
                shutil.rmtree(target_directory)
            except OSError as e:
                 self._print_output("Issue removing the target files -: %s - %s." % (e.filename, e.strerror))
            copy_tree(source_directory, target_directory, update=True, verbose=True)
         self._set_all_files_to_writable(target_directory, False)

    # returns true if the source binaries are a later version number than the target binaries
    def source_assemblies_newer_than_target(self, source_major_version, source_minor_version, target_major_version, target_minor_version):
        if source_major_version > target_major_version:
            return True
        elif source_major_version == target_major_version and source_minor_version > target_minor_version: 
            return True
        return False
    
    # checks whether the service is running.
    def service_is_running(self, retry_times=1):
        try:
            for x in range(retry_times):
                for p in psutil.process_iter():
                    if p.name().lower() == self.fecs_exe_name:
                        return True
                time.sleep(0.200)
        except Exception:
            return False
        return False
    
    # kills the service and ensure the latest version has been copied over and is running
    def version_update_check(self, print_output_message):
        current_valid = self._running_version_is_valid()
        if current_valid is False:
            if print_output_message:
                print("Detected that current locally running version of the configservice is older than the one in source.")
            self._kill_service()
            return self.copy_launch_and_ensure_running()

    # helper method for printing output
    def _print_output(self, message):
        if self.should_print_output:
            print(message)

    # gets the running version
    def _get_running_version(self, retry_times=1):
        # first call here can timeout after we've ran the session, 
        # so set a short http timeout as it should be a quick operation
        try:
            running_service_meta_info = self._session_api_instance.get_service_info(version=self.version, _request_timeout=.300)
            return running_service_meta_info
        except Exception:
            if retry_times > 1:
                time.sleep(1)
                return self._get_running_version(retry_times - 1)
            raise FECServiceNotRunningException("Could not get running service info")
  
    # checks if running version is equal to or greater than the version in the local bin folder
    # returns false if the running version is lower 
    def _running_version_is_valid(self):
        try:
            running_service_meta_info = self._get_running_version(3)
        except:
            return False
        if self.local_version_major_num is None or self.local_version_minor_num is None:
            return True
        if (running_service_meta_info.version == '' or running_service_meta_info == None):
            # service version is empty / unknown so can't be sure to want to update it. 
            return False
        
        if running_service_meta_info.service_version_type == "WindowsService":
            self._print_output("Have detected that you are using the Windows Service version of the fbenvconfigservice -> please uninstall.")
            return False

        if running_service_meta_info.service_version_type == "Developer":
            self._print_output("Skipping version check as Configservice is in developer mode")
            return True

        running_version = running_service_meta_info.version.split('.')
        running_major_version = int(running_version[0])
        running_minor_version = int(running_version[1])
        local_is_newer = self.source_assemblies_newer_than_target(self.local_version_major_num, self.local_version_minor_num, running_major_version, running_minor_version)
        return local_is_newer is False
       
    # Kills the running fbenvconfigservice
    def _kill_service(self):
        try:
            for p in psutil.process_iter():
                if p.name().lower() == self.fecs_exe_name:
                    p.terminate()
                    killed, _ = psutil.wait_procs([p], timeout=3)
                    if not killed:
                        os.kill(p.pid, signal.SIGTERM)
                        time.sleep(2)
                    break
            for p in psutil.process_iter():
                if p.name().lower() == self.fecs_exe_name:
                    return False
            return True
        except Exception as ex:
            self._print_output("Error killing the service: " + str(ex))
            return False 
    
    # checks whether we're currently running as admin
    def _is_admin(self):
        try:
            is_admin = os.getuid() == 0
        except AttributeError:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        return is_admin

    # gets the local version number of the configservice
    def _get_local_bin_version_number(self, fbenvconfigservice_path = None):
        if fbenvconfigservice_path is None:
            fbenvconfigservice_working_dir = os.path.join(self.gameroot,  self.fecs_exe_path)
            fbenvconfigservice_path = os.path.join(fbenvconfigservice_working_dir,  self.fecs_exe_name)
        if os.path.exists(fbenvconfigservice_path) is False:
                self._print_output("No local version of the fbenvconfigservice found at " + fbenvconfigservice_path + " if this is unexepected please ensure the folders are sync'd correctly")
                return (None, None)  
        return self._get_file_version_info(fbenvconfigservice_path)
    
    # gets the remove version number
    def _get_remote_bin_version_number(self):
        remote_configservice_path = os.path.join(self.fbenvconfigservice_target_working_dir, self.fecs_exe_name)
        if os.path.exists(remote_configservice_path) is False:
            self._print_output("No deployed version of the fbenvconfigservice found at " + remote_configservice_path)
            return (None, None)
        return self._get_file_version_info(remote_configservice_path)
    
    # returns the file version info of a file as int tuple representing (maj, min) 
    def _get_file_version_info(self, path):      
            try:      
                info = GetFileVersionInfo(path, "\\")
                ms = info['FileVersionMS']            
                return int(HIWORD (ms)), int(LOWORD (ms))
            except:
                self._check_for_open_handles()
                raise ServiceFilesQueryException("Error getting file version info for " + path)

    def _check_for_open_handles(self):
        for proc in psutil.process_iter():
            try:
                if proc.name().lower() == self.fecs_exe_name:
                    proc.terminate()
                else:
                    flist = proc.open_files()
                    if flist:
                        for nt in flist:
                            if nt.path.startswith(self.fbenvconfigservice_target_working_dir):
                                self._print_output("We have a handle open on the file: " + nt.path)
                                self._print_output("Handle is open by process: " + proc.name())
                                self._print_output("Please ensure this process is closed before restarting the application")
                                return
            except Exception as ex:
                if any(proc.name().lower() in filename for filename in self.file_interest_list):
                    self._print_output("Error checking for open handles for file " + proc.name() + "\terror: " + str(ex))
        

    # just launch the deployed service
    def _launch_service(self):
            service = os.path.join(self.fbenvconfigservice_target_working_dir, self.fecs_exe_name)
            subprocess.Popen(service, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, cwd=self.fbenvconfigservice_target_working_dir, close_fds=True, creationflags = subprocess.DETACHED_PROCESS, shell=False)
            
            self._print_output("Launched fbenvconfigservice process at " +service)
            if self.service_is_running(retry_times=10) is False:
                return FECServiceNotRunningException("Service is not running.")
            return self._get_running_version(5)
            
    
    # iterate over files in the specified directory and make writable
    def _set_all_files_to_writable(self, working_directory: str, remove_path: bool = False):
        for root, _, files in os.walk(working_directory):
            for filename in files:
                filepath = os.path.join(root, filename)
                try:
                    fbfileio.set_writable(filepath, True)
                except Exception as ex:
                    print(ex)
                if remove_path:
                    os.remove(filepath)
