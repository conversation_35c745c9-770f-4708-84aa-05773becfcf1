import os

# Class used for interacting with the service process - launching, ensuring the right version is installed etc.
class FbenvConfigServiceTools():
    file_lock_name = "fbenv_lock"
    fbenvconfigservice_target_working_dir = os.path.normpath(os.path.join(os.environ.get("localappdata"), "FbEnvConfigService/win-x64"))
    should_print_output = True
    fecs_exe_path = "TnT/Build/FbEnvConfigService/bin/win-x64/"
    fecs_exe_name = "fbenvconfigservice.exe"
    file_interest_list = [fecs_exe_name, "drone.exe", "python.exe", "fbenvconsole.exe", "frostbitestartup.exe", "frostbitesetup.exe"]
    
    def __init__(self, gameroot:str, _session_api_instance):
        # Handle case where gameroot is None
        if gameroot is None:
            # Try to get from environment variable as fallback
            fallback_gameroot = os.environ.get('GAME_ROOT', r'C:\Users\<USER>\Perforce\DICE\hvu_DICE-HVU1_4408')
            gameroot = fallback_gameroot
            print(f"Warning: gameroot was None, using fallback: {fallback_gameroot}")
        
        self.gameroot = gameroot
        self._session_api_instance = _session_api_instance
        self.fbenvconfigservice_local_deploy_dir = os.path.join(self.gameroot,  self.fecs_exe_path)
        self.version = "1"
        self.local_version_major_num, self.local_version_minor_num = self._get_local_bin_version_number()
