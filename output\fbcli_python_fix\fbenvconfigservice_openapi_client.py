"""
Stub module for fbenvconfigservice_openapi_client

This module provides stub implementations for the missing OpenAPI client.
It contains the minimum necessary classes and methods to allow fbcli to run
without the actual OpenAPI client implementation.
"""

# Exception classes
class ApiException(Exception):
    """Base API exception"""
    def __init__(self, status=None, reason=None, message=None):
        self.status = status
        self.reason = reason
        self.message = message or str(reason)
        super(ApiException, self).__init__(self.message)

class NotFoundException(ApiException):
    """Not found exception"""
    pass

class ServiceException(ApiException):
    """Service exception"""
    pass

class ForbiddenException(ApiException):
    """Forbidden exception"""
    pass

# Configuration class
class Configuration:
    """Configuration class for API client"""
    def __init__(self):
        self.host = 'http://localhost:7225'
        self.retries = 3
        self.timeout = 30
        self.verify_ssl = True

# API Client class
class ApiClient:
    """API Client class"""
    def __init__(self, configuration=None):
        self.configuration = configuration or Configuration()
        self.default_headers = {}
        self.cookie = None

    def call_api(self, *args, **kwargs):
        """Stub for API calls"""
        raise ServiceException(503, "Service Unavailable", "FbEnvConfigService is not available")

# Model classes
class BaseModel:
    """Base model class"""
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

class InitializeDataObject(BaseModel):
    """Initialize data object"""
    pass

class SetUserSettingDataObject(BaseModel):
    """Set user setting data object"""
    pass

class ClearUserSettingDataObject(BaseModel):
    """Clear user setting data object"""
    pass

# API classes
class BaseApi:
    """Base API class"""
    def __init__(self, api_client=None):
        self.api_client = api_client or ApiClient()

    def _handle_request_error(self, error_message="Service unavailable"):
        """Handle request errors consistently"""
        raise ServiceException(503, "Service Unavailable", error_message)

class SessionApi(BaseApi):
    """Session API"""
    def __init__(self, api_client=None):
        super(SessionApi, self).__init__(api_client)

    def initialize_session(self, *args, **kwargs):
        self._handle_request_error("FbEnvConfigService session initialization failed")

    def load_session(self, *args, **kwargs):
        self._handle_request_error("FbEnvConfigService session loading failed")

    def save_session(self, *args, **kwargs):
        self._handle_request_error("FbEnvConfigService session saving failed")

class LicenseeApi(BaseApi):
    """Licensee API"""
    def __init__(self, api_client=None):
        super(LicenseeApi, self).__init__(api_client)

    def get_licensee_info(self, *args, **kwargs):
        self._handle_request_error("FbEnvConfigService licensee info retrieval failed")

class DatadirApi(BaseApi):
    """Datadir API"""
    def __init__(self, api_client=None):
        super(DatadirApi, self).__init__(api_client)

    def get_datadir_info(self, *args, **kwargs):
        self._handle_request_error("FbEnvConfigService datadir info retrieval failed")

class LocalrootApi(BaseApi):
    """Localroot API"""
    def __init__(self, api_client=None):
        super(LocalrootApi, self).__init__(api_client)

    def get_localroot_info(self, *args, **kwargs):
        self._handle_request_error("FbEnvConfigService localroot info retrieval failed")

class UserSettingsApi(BaseApi):
    """User Settings API"""
    def __init__(self, api_client=None):
        super(UserSettingsApi, self).__init__(api_client)

    def get_user_settings(self, *args, **kwargs):
        self._handle_request_error("FbEnvConfigService user settings retrieval failed")

    def set_user_setting(self, *args, **kwargs):
        self._handle_request_error("FbEnvConfigService user setting update failed")

    def clear_user_setting(self, *args, **kwargs):
        self._handle_request_error("FbEnvConfigService user setting clearing failed")

# Create the api and model sub-modules
class ApiModule:
    """API module container"""
    def __init__(self):
        self.session_api = SessionApi
        self.licensee_api = LicenseeApi
        self.datadir_api = DatadirApi
        self.localroot_api = LocalrootApi
        self.user_settings_api = UserSettingsApi

class ModelModule:
    """Model module container"""
    def __init__(self):
        self.initialize_data_object = InitializeDataObject
        self.set_user_setting_data_object = SetUserSettingDataObject
        self.clear_user_setting_data_object = ClearUserSettingDataObject

class ExceptionsModule:
    """Exceptions module container"""
    def __init__(self):
        self.ApiException = ApiException
        self.NotFoundException = NotFoundException
        self.ServiceException = ServiceException
        self.ForbiddenException = ForbiddenException

# Create module instances
api = ApiModule()
model = ModelModule()
exceptions = ExceptionsModule()

# Export main classes at module level
__all__ = [
    'Configuration',
    'ApiClient',
    'InitializeDataObject',
    'SetUserSettingDataObject',
    'ClearUserSettingDataObject',
    'SessionApi',
    'LicenseeApi',
    'DatadirApi',
    'LocalrootApi',
    'UserSettingsApi',
    'ApiException',
    'NotFoundException',
    'ServiceException',
    'ForbiddenException',
    'api',
    'model',
    'exceptions'
]
