"""
expire.py

Module that handles the expiration of folders (builds) using bilbo
Can be used to keep X number of files/folders based on certain strategies.
"""

from __future__ import absolute_import
from builtins import str
from datetime import datetime, timedelta
from functools import wraps
import os
import re
import concurrent.futures
from elipy2 import LOGGER, SETTINGS, build_metadata_utils, core, filer
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


def safe_execute(default_return=None, log_errors=True):
    """Decorator for safe execution with error handling"""

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as exc:  # pylint: disable=broad-exception-caught
                # Broad exception handling is intentional for safe execution wrapper
                if log_errors:
                    LOGGER.error("Error in %s: %s", func.__name__, exc)
                return default_return

        return wrapper

    return decorator


class ExpireUtils:
    """
    Utils class for Expiring Builds
    """

    def __init__(self):
        self.bilbo = build_metadata_utils.setup_metadata_manager()

    @collect_metrics()
    def expire(
        self,
        path,
        maxamount,
        dry_run,
        use_onefs_api=False,
        release_candidates_to_keep_count=1,
    ):
        """
        Keeps up to $maxamount of items at $path.

        Deletion priority (aligned with PowerShell script):
        1. Orphan builds first (if older than 2 days)
        2. Then retention-based deletion: spin, smoke, release candidate, promoted
        3. Finally count-based retention

        Args:
            path: Path to scan for builds
            maxamount: Maximum number of builds to keep
            dry_run: Whether to perform actual deletion
            use_onefs_api: Whether to use OneFS API for deletion
            release_candidates_to_keep_count: Number of release candidates to preserve
        """
        builds_to_expire, orphan_builds = self.get_builds_to_expire(
            path, maxamount, release_candidates_to_keep_count
        )

        total_builds = len(builds_to_expire) + len(orphan_builds)

        if total_builds == 0:
            LOGGER.info("No builds to delete at %s", path)
            return

        LOGGER.info(
            "Deletion plan for %s: %d total builds (%d orphan priority, %d retention-based)",
            path,
            total_builds,
            len(orphan_builds),
            len(builds_to_expire),
        )

        failed_deletions = []

        # Delete orphan builds first (highest priority)
        if orphan_builds:
            LOGGER.info(
                "Phase 1: Deleting %d orphan builds (priority deletion)", len(orphan_builds)
            )
            failed_deletions.extend(
                self._process_build_deletions(orphan_builds, dry_run, use_onefs_api, False)
            )

        # Then delete retention-based builds
        if builds_to_expire:
            LOGGER.info(
                "Phase 2: Deleting %d builds based on retention policy", len(builds_to_expire)
            )
            failed_deletions.extend(
                self._process_build_deletions(builds_to_expire, dry_run, use_onefs_api, True)
            )

        # Report any failures
        if failed_deletions:
            LOGGER.error("Failed to delete %d builds:", len(failed_deletions))
            for failed_deletion in failed_deletions:
                LOGGER.error(
                    "  %s deletion failed (%s)",
                    failed_deletion["build"].id,
                    failed_deletion["exc"],
                )
        else:
            LOGGER.info("All planned deletions completed successfully")

    def get_builds_to_expire(self, path, maxamount, release_candidates_to_keep_count):
        """
        Returns an iterable of builds which are candidates to delete.

        Priority order (aligned with PowerShell script):
        1. Orphan builds first (if older than 2 days)
        2. Then apply retention criteria: spin, smoke, release candidate, promoted
        3. Finally apply count-based retention
        """
        # Scan and filter builds
        disk_builds = self._scan_disk_builds(path)
        disk_builds_filtered, orphaned_builds = self._process_orphaned_builds(disk_builds)
        disk_builds_sorted = self._sort_builds_by_cl(disk_builds_filtered)

        maxamounti = int(maxamount)

        # Check if orphan deletion is sufficient
        if self._orphan_deletion_sufficient(orphaned_builds, disk_builds_sorted, maxamounti):
            return [], orphaned_builds

        # Check if no deletion is needed
        if self._no_deletion_needed(orphaned_builds, disk_builds_sorted, maxamounti):
            return [], []

        # Apply retention policies
        expire_list = self._apply_retention_policies(
            disk_builds_sorted, maxamounti, release_candidates_to_keep_count
        )

        LOGGER.info(
            "Deletion plan: %d orphan builds (priority), %d regular builds (retention)",
            len(orphaned_builds),
            len(expire_list),
        )

        return expire_list, orphaned_builds

    def _orphan_deletion_sufficient(self, orphaned_builds, disk_builds_sorted, maxamounti):
        """Check if orphan deletion alone is sufficient."""
        if orphaned_builds and len(disk_builds_sorted) <= maxamounti:
            LOGGER.info(
                "After orphan deletion, builds available %s <= %s maximum builds",
                len(disk_builds_sorted),
                maxamounti,
            )
            return True
        return False

    def _no_deletion_needed(self, orphaned_builds, disk_builds_sorted, maxamounti):
        """Check if no deletion is needed."""
        if not orphaned_builds and len(disk_builds_sorted) <= maxamounti:
            LOGGER.info(
                "Nothing to delete! builds available %s <= %s maximum builds",
                len(disk_builds_sorted),
                maxamounti,
            )
            return True
        return False

    def _apply_retention_policies(
        self, disk_builds_sorted, maxamounti, release_candidates_to_keep_count
    ):
        """Apply retention policies to determine which builds to expire."""
        reserve_list = disk_builds_sorted[-maxamounti:]
        expire_list = disk_builds_sorted[:-maxamounti]

        if release_candidates_to_keep_count > 0:
            preservation_counters = self._get_preservation_counters()
            expire_list = self._filter_builds_by_retention(
                expire_list, preservation_counters, reserve_list
            )

        return expire_list

    def _filter_builds_by_retention(self, expire_list, preservation_counters, reserve_list):
        """Filter builds based on retention criteria."""
        filtered_expire_list = []
        for build in expire_list:
            try:
                all_bilbo_builds = self._query_bilbo_for_build(build)
                should_preserve, reason = self._should_preserve_build_with_retention(
                    all_bilbo_builds, preservation_counters
                )
                if should_preserve:
                    LOGGER.info("Preserving build %s (%s)", build, reason)
                    reserve_list.append(build)
                else:
                    filtered_expire_list.append(build)
            except Exception as exc:  # pylint: disable=broad-exception-caught
                # Broad exception handling for build processing robustness
                LOGGER.warning("Error checking retention for build %s: %s", build, exc)
                # On error, default to deleting the build (conservative approach)
                filtered_expire_list.append(build)
        return filtered_expire_list

    def expire_old_folders(self, path, number, dry_run=False, use_onefs_api=False):
        """
        Keeps the newest $number of folders and deletes the rest
        """
        if not os.path.exists(path):
            LOGGER.warning("Path does not exist: %s", path)
            return

        sorted_folders = sorted(
            list(os.listdir(path)),
            key=lambda x: os.path.getmtime(os.path.join(path, x)),
        )

        n_delete = len(sorted_folders) - number
        if n_delete <= 0:
            LOGGER.info(
                "Found %d builds at %s but asked to keep %d, doing nothing.",
                len(sorted_folders),
                path,
                number,
            )
            return

        LOGGER.info("Found %d builds at %s.", len(sorted_folders), path)

        def delete_folder(folder):
            folder_path = os.path.join(path, folder)
            creation_date = datetime.fromtimestamp(os.path.getmtime(folder_path))
            action = "Deleting" if not dry_run else "Would delete"
            LOGGER.info("%s %s [Created: %s]", action, folder_path, creation_date)
            if not dry_run:
                if use_onefs_api:
                    filer.FilerUtils.delete_with_onefs_api(folder_path)
                else:
                    core.delete_folder(folder_path)

        if not dry_run:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                executor.map(delete_folder, sorted_folders[:n_delete])
        else:
            for folder in sorted_folders[:n_delete]:
                delete_folder(folder)

    @staticmethod
    def keep_n_at_path(path, number, dry_run, use_onefs_api=False):
        """
        Removes all folders at $path, except for the $number newest folders.
        """
        sorted_folders = sorted(
            list(os.listdir(path)),
            key=lambda x: os.path.getmtime(os.path.join(path, x)),
        )

        n_delete = len(sorted_folders) - number
        if n_delete <= 0:
            LOGGER.info(
                "Found %d builds at %s but asked to keep %d, doing nothing.",
                len(sorted_folders),
                path,
                number,
            )
            return

        LOGGER.info("Found %d builds at %s.", len(sorted_folders), path)

        def delete_folder(folder):
            folder_path = os.path.join(path, folder)
            creation_date = datetime.fromtimestamp(os.path.getmtime(folder_path))
            action = "Deleting" if not dry_run else "Would delete"
            LOGGER.info("%s %s [Created: %s]", action, folder_path, creation_date)
            if not dry_run:
                if use_onefs_api:
                    filer.FilerUtils.delete_with_onefs_api(folder_path)
                else:
                    core.delete_folder(folder_path)

        if not dry_run:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                executor.map(delete_folder, sorted_folders[:n_delete])
        else:
            for folder in sorted_folders[:n_delete]:
                delete_folder(folder)

    def _scan_disk_builds(self, path):
        """Scan disk directories and return list of build paths using fixed-depth approach"""
        if not os.path.exists(path):
            LOGGER.warning("Path does not exist: %s", path)
            return []

        builds = []  # Use simplified fixed-depth scanning for frosty and webexport builds
        if "frosty" in path.lower():
            builds = self._scan_frosty_builds_fixed_depth(path)
        elif "webexport" in path.lower():
            builds = self._scan_webexport_builds_fixed_depth(path)
        else:
            # Use original recursive scanning for other build types
            self._scan_for_builds(path, builds, set())

        unique_builds = list(dict.fromkeys(builds))  # Deduplicate
        LOGGER.info("Found %d builds on disk at %s", len(unique_builds), path)

        # Debug logging for CH1-event issue
        if "frosty" in path.lower() and "battlefieldgame" in path.lower():
            # Extract branch name from path for debugging
            path_parts = path.replace("\\", "/").split("/")
            if len(path_parts) > 0:
                branch_name = path_parts[-1]
                if branch_name.lower() == "ch1-event":
                    LOGGER.info("DEBUG: CH1-event branch scan results:")
                    LOGGER.info("DEBUG: Path: %s", path)
                    LOGGER.info("DEBUG: Builds found: %d", len(unique_builds))
                    if unique_builds:
                        LOGGER.info("DEBUG: Sample builds: %s", unique_builds[:3])
                        # Log all builds for comparison with PowerShell script
                        LOGGER.info("DEBUG: All builds found:")
                        for i, build in enumerate(unique_builds, 1):
                            normalized_build_path = build.replace("/", "\\")
                            LOGGER.info("DEBUG:   %d. %s", i, normalized_build_path)
                    else:
                        LOGGER.info("DEBUG: No builds found - checking if path exists")
                        LOGGER.info("DEBUG: Path exists: %s", os.path.exists(path))

        return unique_builds

    def _scan_frosty_builds_fixed_depth(self, path):
        """Scan for frosty builds using a consolidated fixed depth approach

        Frosty structure: frosty/BattlefieldGame/branch/CL1/branch/CL2

        This function handles two cases:
        1. Parent path (e.g., frosty/BattlefieldGame): scans all branches
        2. Specific branch path (e.g., frosty/BattlefieldGame/CH1-event): scans just that branch

        Structure levels:
        - Level 1: Branch directories (ch1-event, ch1-release, etc.)
        - Level 2: CL directories (24269306, etc.)
        - Level 3: Branch directories again (CH1-event, etc.)
        - Level 4: Final CL directories (24269306, etc.) - these are the actual builds
        """
        builds = []

        if not os.path.exists(path):
            return builds

        # Determine if this is a parent path or specific branch path
        path_parts = path.replace("\\", "/").split("/")
        is_specific_branch = False

        # Check if the last part of the path looks like a branch name
        # (contains letters, not just numbers like CL directories)
        if path_parts and not path_parts[-1].isdigit():
            # Check if this path contains CL directories directly (indicating it's a branch path)
            try:
                entries = os.listdir(path)
                has_cl_dirs = any(
                    entry.isdigit() and len(entry) >= 7 and os.path.isdir(os.path.join(path, entry))
                    for entry in entries
                )
                if has_cl_dirs:
                    is_specific_branch = True
            except (OSError, PermissionError):
                pass

        if is_specific_branch:
            # This is a specific branch path - scan starting from level 2 (CL1 directories)
            builds = self._scan_frosty_branch_builds(path)
        else:
            # This is a parent path - scan all branches starting from level 1
            try:
                level1_entries = os.listdir(path)
            except (OSError, PermissionError) as exc:
                LOGGER.warning("Error scanning frosty path %s: %s", path, exc)
                return builds

            for branch1_entry in level1_entries:
                branch1_path = os.path.join(path, branch1_entry)
                if not os.path.isdir(branch1_path):
                    continue

                # Scan this specific branch
                branch_builds = self._scan_frosty_branch_builds(branch1_path)
                builds.extend(branch_builds)

        return builds

    def _scan_frosty_branch_builds(self, branch_path):
        """Scan builds for a specific frosty branch

        Starting from a branch path (e.g., frosty/BattlefieldGame/CH1-event):
        - Level 2: CL directories (24269306, etc.)
        - Level 3: Branch directories again (CH1-event, etc.)
        - Level 4: Final CL directories (24269306, etc.) - these are the actual builds
        """
        builds = []

        try:
            # Level 2: scan CL directories within the branch directory
            level2_entries = os.listdir(branch_path)
        except (OSError, PermissionError) as exc:
            LOGGER.debug("Error scanning level 2 in %s: %s", branch_path, exc)
            return builds

        for cl1_entry in level2_entries:
            cl1_path = os.path.join(branch_path, cl1_entry)
            if not os.path.isdir(cl1_path):
                continue

            # Level 3: scan branch directories within each CL directory
            try:
                level3_entries = os.listdir(cl1_path)
            except (OSError, PermissionError) as exc:
                LOGGER.debug("Error scanning level 3 in %s: %s", cl1_path, exc)
                continue

            for branch2_entry in level3_entries:
                branch2_path = os.path.join(cl1_path, branch2_entry)
                if not os.path.isdir(branch2_path):
                    continue

                # Level 4: scan final CL directories within each branch directory
                try:
                    level4_entries = os.listdir(branch2_path)
                except (OSError, PermissionError) as exc:
                    LOGGER.debug("Error scanning level 4 in %s: %s", branch2_path, exc)
                    continue

                for cl2_entry in level4_entries:
                    cl2_path = os.path.join(branch2_path, cl2_entry)
                    if os.path.isdir(cl2_path) and self._is_cl_directory(cl2_entry):
                        builds.append(cl2_path)

        return builds

    def _scan_webexport_builds_fixed_depth(self, path):
        """Scan for webexport builds using a consolidated fixed depth approach

        Webexport structure: webexport/kin-live/7.1.0/5102947_20131367
        From the given path (e.g., webexport/kin-live):
        - Level 1: Version directories (7.1.0)
        - Level 2: CL directories (5102947_20131367) - these are the actual builds
        """
        builds = []

        if not os.path.exists(path):
            return builds

        try:
            level1_entries = os.listdir(path)
        except (OSError, PermissionError) as exc:
            LOGGER.warning("Error scanning webexport path %s: %s", path, exc)
            return builds

        # Level 1: direct subdirectories (version numbers or other directories)
        for entry in level1_entries:
            entry_path = os.path.join(path, entry)
            if not os.path.isdir(entry_path):
                continue

            # Level 2: scan CL directories within each version directory
            try:
                level2_entries = os.listdir(entry_path)
            except (OSError, PermissionError) as exc:
                LOGGER.debug("Error scanning level 2 in %s: %s", entry_path, exc)
                continue

            for cl_entry in level2_entries:
                cl_path = os.path.join(entry_path, cl_entry)
                if os.path.isdir(cl_path) and self._is_cl_directory(cl_entry):
                    builds.append(cl_path)

        return builds

    @safe_execute(default_return=None)
    def _scan_for_builds(self, current_path, builds, processed_paths, **kwargs):
        """Unified scanning method for builds when frosty/webexport fixed depth doesn't apply"""
        depth = kwargs.get("depth", 0)
        max_depth = kwargs.get("max_depth", 10)

        if current_path in processed_paths or depth > max_depth:
            return

        processed_paths.add(current_path)

        try:
            entries = os.listdir(current_path)
        except (OSError, PermissionError):
            return

        cl_dirs = []
        other_dirs = []

        for entry in entries:
            entry_path = os.path.join(current_path, entry)
            if os.path.isdir(entry_path):
                if self._is_cl_directory(entry):
                    cl_dirs.append(entry_path)
                else:
                    other_dirs.append(entry_path)

        # Process CL directories
        if cl_dirs:
            for cl_dir in cl_dirs:
                builds.append(cl_dir)
        else:  # No CLs found, continue scanning subdirectories
            for subdir in other_dirs:
                self._scan_for_builds(
                    subdir, builds, processed_paths, depth=depth + 1, max_depth=max_depth
                )

    def _is_cl_directory(self, entry):
        """Check if entry is a build directory (CL number or version)"""
        # Logic for CL numbers (7,8 or 9 digits to handle both old and new CLs)
        if entry.isdigit() and len(entry) >= 7 and len(entry) <= 9:
            return True
        # Additional logic for version numbers (webexport builds)
        version_pattern = r"^\d+\.\d+\.\d+$"  # Matches x.y.z format
        if re.match(version_pattern, entry):
            return True
        # Additional logic for CL_CL format (e.g., 23755029_23755029)
        cl_underscore_pattern = r"^\d{7,9}_\d{7,9}$"  # Matches 7-8-9digits_7-8-9digits format
        if re.match(cl_underscore_pattern, entry):
            return True

        return False

    def _sort_builds_by_cl(self, builds):
        """Sort builds by CL number (oldest to latest)"""

        def extract_cl(build_path):
            path_parts = build_path.replace("\\", "/").split("/")
            cl_numbers = [int(part) for part in reversed(path_parts) if part.isdigit()]
            return cl_numbers[0] if cl_numbers else 0

        return sorted(builds, key=extract_cl)

    @safe_execute(default_return=[])
    def _query_bilbo_for_build(self, build_path):
        """Query Bilbo for a specific build using path matching"""
        matching_builds = list(self.bilbo.get_builds_matching(build_path))
        return matching_builds

    def _process_orphaned_builds(self, disk_builds):
        """
        Process orphaned builds and filter builds for retention processing.

        Aligned with PowerShell script logic:
        - Orphan builds are prioritized for deletion
        - Only delete orphan builds if they are older than 2 days
        - Orphan builds that are newer than 2 days are kept and treated as regular builds
        """
        LOGGER.info("Processing orphaned builds for %d builds on disk", len(disk_builds))

        orphaned_builds = []
        filtered_builds = []

        for build in disk_builds:
            bilbo_builds = self._query_bilbo_for_build(build)

            if not bilbo_builds:
                # This is an orphan build (not found in Bilbo)
                try:
                    # Check if the build is newer than 2 days to avoid deleting builds being copied
                    build_mtime = os.path.getmtime(build)
                    age = datetime.now() - datetime.fromtimestamp(build_mtime)

                    if age < timedelta(days=2):
                        LOGGER.info(
                            "Build %s is an orphan but is newer than 2 days "
                            "(age: %.1f hours), keeping for now",
                            build,
                            age.total_seconds() / 3600,
                        )
                        # Treat as regular build for now, will be checked again later
                        filtered_builds.append(build)
                        continue

                    LOGGER.warning(
                        "Found orphaned build older than 2 days (age: %.1f days): %s",
                        age.days + age.seconds / 86400.0,
                        build,
                    )
                    # This orphan build is old enough to be deleted
                    orphaned_builds.append(build)

                except OSError as exc:
                    LOGGER.error(
                        "Error checking age for build %s: %s. Treating as orphan for deletion.",
                        build,
                        exc,
                    )
                    # If we can't check the age, assume it's old enough to delete
                    orphaned_builds.append(build)
            else:
                # Build found in Bilbo, include in regular retention processing
                filtered_builds.append(build)

        LOGGER.info(
            "Orphaned builds processing complete: %d orphaned (>2 days old), "
            "%d kept for retention processing",
            len(orphaned_builds),
            len(filtered_builds),
        )

        # Additional debugging for CH1-event discrepancy investigation
        total_disk_builds = len(orphaned_builds) + len(filtered_builds)
        if "ch1-event" in str(disk_builds[0]).lower() if disk_builds else False:
            LOGGER.info("DEBUG: CH1-event orphan analysis:")
            LOGGER.info("DEBUG: Total disk builds processed: %d", total_disk_builds)
            LOGGER.info("DEBUG: Orphaned builds (>2 days): %d", len(orphaned_builds))
            LOGGER.info("DEBUG: Filtered builds (for retention): %d", len(filtered_builds))
            LOGGER.info("DEBUG: Original disk builds count: %d", len(disk_builds))

        # Log some details about orphan builds for debugging
        if orphaned_builds:
            LOGGER.info("Orphan builds to be deleted:")
            for orphan in orphaned_builds:  # Log all orphan builds
                try:
                    build_mtime = os.path.getmtime(orphan)
                    age = datetime.now() - datetime.fromtimestamp(build_mtime)
                    # Normalize path to use only backslashes
                    normalized_path = orphan.replace("/", "\\")
                    LOGGER.info(
                        "  %s (age: %.1f days)", normalized_path, age.days + age.seconds / 86400.0
                    )
                except OSError:
                    # Normalize path to use only backslashes
                    normalized_path = orphan.replace("/", "\\")
                    LOGGER.info("  %s (age: unknown)", normalized_path)

        return filtered_builds, orphaned_builds

    def _process_build_deletions(self, builds, dry_run, use_onefs_api, delete_from_bilbo):
        """Helper method to process deletions for a list of builds"""
        failed_deletions = []

        for build in builds:
            LOGGER.info("about to delete: file:%s", build)

            if dry_run or self._has_invalid_filename(build):
                continue

            try:
                self._delete_build_from_disk(build, use_onefs_api)
                if delete_from_bilbo:
                    self._delete_all_matching_builds_from_bilbo(build)
            except ELIPYException as exc:
                failed_deletions.append({"build": build, "exc": exc})

        return failed_deletions

    def _has_invalid_filename(self, build):
        """Check if build has invalid filename characters"""
        return any(char in str(build) for char in ["?", "*"])

    def _delete_build_from_disk(self, build, use_onefs_api):
        """Delete build from disk using appropriate method"""
        LOGGER.info("deleting from disk: file:%s", build)
        if use_onefs_api:
            filer.FilerUtils.delete_with_onefs_api(build)
        else:
            core.delete_folder_with_robocopy(build)

    @safe_execute()
    def _delete_all_matching_builds_from_bilbo(self, build):
        """Delete all builds from bilbo that match the given build path pattern"""
        build_path = build.id if hasattr(build, "id") else str(build)
        matching_builds = list(self.bilbo.get_builds_matching(build_path))

        if not matching_builds:
            # Don't attempt to delete from Bilbo if no builds found
            # This prevents creating new ES entries for non-existent builds
            LOGGER.info(
                "No builds found in Bilbo for path: %s, skipping Bilbo deletion",
                build_path,
            )
            return

        deleted_count = 0
        for matching_build in matching_builds:
            try:
                # Before attempting deletion, verify the build actually exists in ES
                # This prevents the delete_build from creating phantom entries
                existing_builds = list(self.bilbo.get_builds_matching(matching_build.id))
                if not existing_builds:
                    LOGGER.info(
                        "Build %s not found in ES, skipping deletion",
                        matching_build.id,
                    )
                    continue

                LOGGER.info("marking as deleted in bilbo: file:%s", matching_build.id)
                self.bilbo.delete_build(matching_build.id)
                deleted_count += 1
            except Exception as exc:  # pylint: disable=broad-exception-caught
                # Broad exception handling to continue deleting other builds on individual failures
                LOGGER.error("Failed to delete build %s from bilbo: %s", matching_build.id, exc)

        LOGGER.info(
            "Successfully deleted %d/%d builds from bilbo for path: %s",
            deleted_count,
            len(matching_builds),
            build_path,
        )

    def _get_preservation_counters(self):
        """
        Initialize preservation counters with settings from configuration.

        Aligned with PowerShell script priority order:
        1. Spin builds (by branch)
        2. Smoke builds (by branch)
        3. Release candidate builds
        4. Promoted builds
        5. Drone builds (legacy)
        """
        counters = {
            "promoted": 0,
            "drone": 0,
            "release_candidate": 0,  # Add release candidate counter
        }

        # Add retention counters for spin and smoke builds
        for build_type in ["spin", "smoke"]:
            try:
                retention_setting = SETTINGS.get(f"{build_type}_retention", location=None)
                if retention_setting is None:
                    retention_setting = []
            except Exception:
                retention_setting = []  # Safe fallback

            if isinstance(retention_setting, list):
                for retention_dict in retention_setting:
                    if isinstance(retention_dict, dict):
                        for branch in retention_dict:
                            counters[f"{build_type}_{branch}"] = 0

        LOGGER.debug("Initialized preservation counters: %s", list(counters.keys()))
        return counters

    def _should_preserve_build_with_retention(self, all_bilbo_builds, preservation_counters):
        """
        Check if a build should be preserved based on retention criteria.

        Priority order (aligned with PowerShell script):
        1. Spin builds (by branch)
        2. Smoke builds (by branch)
        3. Release candidate builds
        4. Promoted builds
        5. Drone builds (legacy support)

        Note: Orphan builds are handled separately and have highest priority for deletion
        """
        if not all_bilbo_builds:
            return False, ""

        for bilbo_build in all_bilbo_builds:
            result = self._check_build_preservation(bilbo_build, preservation_counters)
            if result[0]:  # If should preserve
                return result

        return False, ""

    def _check_build_preservation(self, bilbo_build, preservation_counters):
        """Check if a single build should be preserved."""
        if not (bilbo_build and bilbo_build.source):
            return False, ""

        source = bilbo_build.source
        build_type = source.get("type", "").lower()
        branch = source.get("branch", "").lower()

        # Check each preservation type in priority order
        preservation_checks = [
            self._check_spin_preservation,
            self._check_smoke_preservation,
            self._check_release_candidate_preservation,
            self._check_promoted_preservation,
            self._check_drone_preservation,
        ]

        for check_func in preservation_checks:
            should_preserve, reason = check_func(source, build_type, branch, preservation_counters)
            if should_preserve:
                return True, reason

        return False, ""

    def _check_spin_preservation(
        self, source, build_type, branch, preservation_counters
    ):  # pylint: disable=unused-argument
        """Check spin build preservation."""
        if build_type != "spin":
            return False, ""

        try:
            return self._check_branch_retention("spin", branch, preservation_counters)
        except Exception as exc:  # pylint: disable=broad-exception-caught
            # Broad exception handling for settings processing robustness
            LOGGER.debug("Error checking spin retention: %s", exc)
            return False, ""

    def _check_smoke_preservation(
        self, source, build_type, branch, preservation_counters
    ):  # pylint: disable=unused-argument
        """Check smoke build preservation."""
        if build_type != "smoke":
            return False, ""

        try:
            return self._check_branch_retention("smoke", branch, preservation_counters)
        except Exception as exc:  # pylint: disable=broad-exception-caught
            # Broad exception handling for settings processing robustness
            LOGGER.debug("Error checking smoke retention: %s", exc)
            return False, ""

    def _check_branch_retention(self, build_type, branch, preservation_counters):
        """Check branch-based retention settings."""
        retention_setting = SETTINGS.get(f"{build_type}_retention", location=None)
        if not (retention_setting and isinstance(retention_setting, list)):
            return False, ""

        for retention_dict in retention_setting:
            if not isinstance(retention_dict, dict):
                continue

            for config_branch, branch_retention in retention_dict.items():
                if config_branch.lower() == branch:
                    counter_key = f"{build_type}_{config_branch}"
                    if preservation_counters[counter_key] < branch_retention:
                        preservation_counters[counter_key] += 1
                        return True, f"{build_type} {config_branch} build"

        return False, ""

    def _check_release_candidate_preservation(
        self, source, build_type, branch, preservation_counters  # pylint: disable=unused-argument
    ):
        """Check release candidate preservation."""
        if (
            source.get("release_candidate")
            and preservation_counters.get("release_candidate", 0) < 1
        ):
            preservation_counters["release_candidate"] = (
                preservation_counters.get("release_candidate", 0) + 1
            )
            return True, "release candidate build"
        return False, ""

    def _check_promoted_preservation(
        self, source, build_type, branch, preservation_counters
    ):  # pylint: disable=unused-argument
        """Check promoted build preservation."""
        if "build_promotion_level" in source and preservation_counters["promoted"] < 1:
            preservation_counters["promoted"] += 1
            return True, "promoted build"
        return False, ""

    def _check_drone_preservation(
        self, source, build_type, branch, preservation_counters
    ):  # pylint: disable=unused-argument
        """Check drone build preservation (legacy)."""
        if build_type == "drone" and preservation_counters["drone"] < 2:
            preservation_counters["drone"] += 1
            return True, "drone build"
        return False, ""
